# hlsloptconv - HLSL Shader Optimizer and Converter

A powerful HLSL (High-Level Shading Language) compiler and optimizer that supports comprehensive conversion and optimization between HLSL and GLSL formats. This tool can parse, analyze, optimize, and generate shader code in multiple formats with advanced optimization capabilities.

## ✨ Key Features

- **🔄 Multi-format Support**: Convert between HLSL and GLSL seamlessly
- **⚡ Advanced Optimizations**: Mathematical, constant folding, and dead code elimination
- **🎮 Graphics API Demo**: Real-time demonstration with DirectX and OpenGL
- **🛠️ Cross-platform**: Core functionality works on Windows, Linux, and macOS
- **📊 Optimization Statistics**: Detailed reports on applied optimizations

## 🚀 Quick Start

### Installation

1. **Download or clone** the repository
2. **Build** using the provided build script:
   ```bash
   # Windows
   build.bat
   
   # Cross-platform
   mkdir build && cd build
   cmake .. && cmake --build .
   ```

### Basic Usage

```bash
# Convert HLSL to GLSL
bin\hlsloptconv.exe -f glsl_450 -s vertex -o output.glsl input.hlsl

# Optimize and display to console
bin\hlsloptconv.exe -f glsl_450 -s pixel -P input.hlsl

# Run graphics demo
bin\four.exe

# Show help
bin\hlsloptconv.exe --help
```

## 📋 Supported Formats

### Input Formats
- **HLSL** - All shader model versions (SM3.0 - SM6.0)
- **Auto-detection** - Automatically detects HLSL input format

### Output Formats
- `hlsl_sm3` - HLSL Shader Model 3.0 (DirectX 9)
- `hlsl_sm4` - HLSL Shader Model 4.0 (DirectX 10)
- `hlsl_sm5` - HLSL Shader Model 5.0 (DirectX 11)
- `hlsl_sm6` - HLSL Shader Model 6.0 (DirectX 12)
- `hlsl_modern` - Modern HLSL with clean syntax
- `glsl_140` - GLSL 1.40 (OpenGL 3.1)
- `glsl_330` - GLSL 3.30 (OpenGL 3.3)
- `glsl_450` - GLSL 4.50 (OpenGL 4.5)
- `glsl_es_100` - GLSL ES 1.00 (OpenGL ES 2.0)
- `glsl_es_300` - GLSL ES 3.00 (OpenGL ES 3.0)
- `glsl_es_320` - GLSL ES 3.20 (OpenGL ES 3.2)

### Shader Types
- **Vertex Shaders** (`-s vertex`)
- **Fragment/Pixel Shaders** (`-s pixel`)
- **Geometry Shaders** (parsing support)
- **Compute Shaders** (parsing support)

## ⚡ Optimization Features

### Mathematical Optimizations
- **Constant Folding**: `2.0 + 3.0` → `5.0`
- **Power Function**: `pow(x, 2.0)` → `x * x`
- **Trigonometric**: `sin(0.0)` → `0.0`, `cos(0.0)` → `1.0`
- **Exponential**: `exp(0.0)` → `1.0`, `log(1.0)` → `0.0`
- **Identity Operations**: `x * 1.0` → `x`, `x + 0.0` → `x`

### Code Optimizations
- **Dead Code Elimination**: Remove unused variables and functions
- **Redundant Operation Removal**: Eliminate unnecessary calculations
- **Vector Operation Optimization**: Optimize swizzling and component access
- **Control Flow Optimization**: Simplify conditional statements

### Advanced Features
- **Texture Sampling Optimization**: Smart sampler type detection
- **Matrix Operation Optimization**: Efficient matrix-vector operations
- **Precision Analysis**: Optimize precision qualifiers
- **Loop Unrolling**: Automatic loop optimization

## 🎮 Graphics Demo (four.exe)

The included `four.exe` demonstrates real-time shader compilation and optimization across multiple graphics APIs:

- **DirectX 9** - Shader Model 3.0 support
- **DirectX 11** - Modern DirectX features
- **OpenGL 2.0** - Legacy OpenGL compatibility
- **OpenGL 3.1** - Modern OpenGL features

### Running the Demo
```bash
# After building
./build/bin/four.exe
```

The demo shows:
- Real-time shader compilation
- Optimization effects visualization
- Multi-API rendering comparison
- Performance impact of optimizations

## 🛠️ Command Line Options

### Basic Options
```bash
-f, --format <fmt>     Output format (required)
-s, --stage <stage>    Shader stage: vertex|pixel (required)
-i, --input <fmt>      Input format (auto-detected if not specified)
-o, --output <file>    Output file (default: stdout)
-P, --stdout           Pretty-print to console
-e, --entrypoint <fn>  Entry point function name (default: main)
```

### Advanced Options
```bash
-d, --dump             Dump AST for debugging
-O, --optimize <level> Optimization level 0-4 (default: 2)
--stats                Show optimization statistics
--backup               Create backup of output file
--fast-math            Enable fast math optimizations
--target <platform>    Target platform: generic|mobile|desktop|console
```

### Preprocessor Options
```bash
-D<name>[=<value>]     Define preprocessor macro
```

## 📁 Project Structure

```
hlsloptconv/
├── build.bat              # Windows build script
├── CMakeLists.txt          # CMake configuration
├── src/                    # Source code
│   ├── common.cpp/.hpp     # Common utilities
│   ├── hlslparser.cpp/.hpp # HLSL parser
│   ├── compiler.cpp/.hpp   # Core compiler
│   ├── optimizer.cpp       # Optimization engine
│   ├── generator.cpp       # Code generation
│   └── tools/              # Executable sources
│       ├── cli.cpp         # hlsloptconv.exe
│       └── four.cpp        # four.exe demo
├── runtests/               # Demo resources
├── tests/                  # Test shaders
└── docs/                   # Documentation
    ├── build.md            # Build instructions
    ├── api_reference.md    # API documentation
    └── examples/           # Usage examples
```

## 🔧 Building from Source

See [build.md](build.md) for detailed build instructions.

### Quick Build
```bash
# Windows (recommended)
build.bat

# Cross-platform
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build .
```

## 📚 Documentation

- **[build.md](build.md)** - Detailed build instructions
- **[api_reference.md](api_reference.md)** - API documentation
- **[comprehensive_guide.md](comprehensive_guide.md)** - Complete technical guide

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [license](license) file for details.

## 🙏 Acknowledgments

- Built with modern C++17 features
- Uses CMake for cross-platform building
- Supports multiple graphics APIs
- Optimized for shader development workflows
