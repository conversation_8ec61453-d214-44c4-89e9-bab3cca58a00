

#include "compiler.hpp"


using namespace HOC;

// Helper function to estimate AST complexity
int EstimateASTComplexity(ASTNode* node, int maxDepth = 10)
{
	if (maxDepth <= 0) return 1000; // Assume high complexity if too deep

	int complexity = 1;
	for (ASTNode* ch = node->firstChild; ch; ch = ch->next)
	{
		complexity += EstimateASTComplexity(ch, maxDepth - 1);
		if (complexity > 100) return complexity; // Early exit for very complex nodes
	}
	return complexity;
}


void ConstantPropagation::PostVisit(ASTNode* node)
{
	// Enhanced unary operation constant folding
	if (auto* unop = dyn_cast<const UnaryOpExpr>(node))
	{
		if (auto* src = dyn_cast<const ConstExpr>(unop->GetSource()))
		{
			switch (src->GetReturnType()->kind)
			{
			case ASTType::Bool:
			{
				bool sv = dyn_cast<const BoolExpr>(src)->value;
				bool out = false;
				switch (unop->opType)
				{
				case STT_OP_Not: out = sv ? 0 : 1; goto unop_replbool;
				default: break;
				unop_replbool:
					delete node->ReplaceWith(new BoolExpr(out, unop->GetReturnType()));
					return;
				}
				break;
			}
			case ASTType::Int32:
			{
				int32_t sv = dyn_cast<const Int32Expr>(src)->value;
				int32_t out = 0;
				switch (unop->opType)
				{
				case STT_OP_Sub: out = -sv; goto unop_replint;
				case STT_OP_Inv: out = ~sv; goto unop_replint;
				default: break;
				unop_replint:
					delete node->ReplaceWith(new Int32Expr(out, unop->GetReturnType()));
					return;
				}
				break;
			}
			case ASTType::Float16:
			case ASTType::Float32:
			{
				double sv = dyn_cast<const Float32Expr>(src)->value;
				double out = 0;
				switch (unop->opType)
				{
				case STT_OP_Sub: out = -sv; goto unop_replfloat;
				default: break;
				unop_replfloat:
					delete node->ReplaceWith(new Float32Expr(out, unop->GetReturnType()));
					return;
				}
				break;
			}
			}
		}
	}
	// Enhanced binary operation constant folding
	else if (auto* binop = dyn_cast<const BinaryOpExpr>(node))
	{
		auto* lft = dyn_cast<const ConstExpr>(binop->GetLft());
		auto* rgt = dyn_cast<const ConstExpr>(binop->GetRgt());
		if (lft && rgt &&
			binop->GetReturnType() == lft->GetReturnType() &&
			binop->GetReturnType() == rgt->GetReturnType())
		{
			switch (binop->GetReturnType()->kind)
			{
			case ASTType::Bool:
			{
				bool lv = dyn_cast<const BoolExpr>(lft)->value;
				bool rv = dyn_cast<const BoolExpr>(rgt)->value;
				bool out = false;
				switch (binop->opType)
				{
				case STT_OP_LogicalAnd: out = lv && rv; goto binop_replbool;
				case STT_OP_LogicalOr: out = lv || rv; goto binop_replbool;
				case STT_OP_Eq: out = lv == rv; goto binop_replbool;
				case STT_OP_NEq: out = lv != rv; goto binop_replbool;
				default: break;
				binop_replbool:
					delete node->ReplaceWith(new BoolExpr(out, binop->GetReturnType()));
					return;
				}
				break;
			}
			case ASTType::Int32:
			{
				int32_t lv = dyn_cast<const Int32Expr>(lft)->value;
				int32_t rv = dyn_cast<const Int32Expr>(rgt)->value;
				int32_t out = 0;
				bool boolOut = false;
				switch (binop->opType)
				{
				case STT_OP_Add: out = lv + rv; goto binop_replint;
				case STT_OP_Sub: out = lv - rv; goto binop_replint;
				case STT_OP_Mul: out = lv * rv; goto binop_replint;
				case STT_OP_Div: out = rv ? lv / rv : 0; goto binop_replint;
				case STT_OP_Mod: out = rv ? lv % rv : 0; goto binop_replint;
				case STT_OP_And: out = lv & rv; goto binop_replint;
				case STT_OP_Or: out = lv | rv; goto binop_replint;
				case STT_OP_Xor: out = lv ^ rv; goto binop_replint;
				case STT_OP_Lsh: out = lv << rv; goto binop_replint;
				case STT_OP_Rsh: out = lv >> rv; goto binop_replint;
				// Comparison operations
				case STT_OP_Less: boolOut = lv < rv; goto binop_replbool_from_int;
				case STT_OP_LEq: boolOut = lv <= rv; goto binop_replbool_from_int;
				case STT_OP_Greater: boolOut = lv > rv; goto binop_replbool_from_int;
				case STT_OP_GEq: boolOut = lv >= rv; goto binop_replbool_from_int;
				case STT_OP_Eq: boolOut = lv == rv; goto binop_replbool_from_int;
				case STT_OP_NEq: boolOut = lv != rv; goto binop_replbool_from_int;
				default: break;
				binop_replint:
					delete node->ReplaceWith(new Int32Expr(out, binop->GetReturnType()));
					return;
				binop_replbool_from_int:
					// Create bool type for comparison result
					delete node->ReplaceWith(new BoolExpr(boolOut, binop->GetReturnType()));
					return;
				}
				break;
			}
			case ASTType::Float16:
			case ASTType::Float32:
			{
				double lv = dyn_cast<const Float32Expr>(lft)->value;
				double rv = dyn_cast<const Float32Expr>(rgt)->value;
				double out = 0;
				bool boolOut = false;
				switch (binop->opType)
				{
				case STT_OP_Add: out = lv + rv; goto binop_replfloat;
				case STT_OP_Sub: out = lv - rv; goto binop_replfloat;
				case STT_OP_Mul: out = lv * rv; goto binop_replfloat;
				case STT_OP_Div: out = rv != 0.0 ? lv / rv : 0.0; goto binop_replfloat;
				case STT_OP_Mod: out = rv != 0.0 ? fmod(lv, rv) : 0.0; goto binop_replfloat;
				// Comparison operations
				case STT_OP_Less: boolOut = lv < rv; goto binop_replbool_from_float;
				case STT_OP_LEq: boolOut = lv <= rv; goto binop_replbool_from_float;
				case STT_OP_Greater: boolOut = lv > rv; goto binop_replbool_from_float;
				case STT_OP_GEq: boolOut = lv >= rv; goto binop_replbool_from_float;
				case STT_OP_Eq: boolOut = abs(lv - rv) < 1e-10; goto binop_replbool_from_float;
				case STT_OP_NEq: boolOut = abs(lv - rv) >= 1e-10; goto binop_replbool_from_float;
				default: break;
				binop_replfloat:
					delete node->ReplaceWith(new Float32Expr(out, binop->GetReturnType()));
					return;
				binop_replbool_from_float:
					delete node->ReplaceWith(new BoolExpr(boolOut, binop->GetReturnType()));
					return;
				}
				break;
			}
			}
		}
	}
	else if (auto* cast = dyn_cast<const CastExpr>(node))
	{
		if (auto* src = dyn_cast<const ConstExpr>(cast->GetSource()))
		{
			if (cast->GetReturnType()->kind == ASTType::Bool)
			{
				bool out = false;
				switch (src->GetReturnType()->kind)
				{
				case ASTType::Bool:
					out = static_cast<const BoolExpr*>(src)->value;
					goto cast_replbool;
				case ASTType::Int32:
					out = static_cast<const Int32Expr*>(src)->value != 0;
					goto cast_replbool;
				case ASTType::Float16:
				case ASTType::Float32:
					out = static_cast<const Float32Expr*>(src)->value != 0;
					goto cast_replbool;
				default: break;
				cast_replbool:
					delete node->ReplaceWith(new BoolExpr(out, cast->GetReturnType()));
					return;
				}
			}
			else if (cast->GetReturnType()->kind == ASTType::Int32)
			{
				int32_t out = 0;
				switch (src->GetReturnType()->kind)
				{
				case ASTType::Bool:
					out = static_cast<const BoolExpr*>(src)->value ? 1 : 0;
					goto cast_replint;
				case ASTType::Int32:
					out = static_cast<const Int32Expr*>(src)->value;
					goto cast_replint;
				case ASTType::Float16:
				case ASTType::Float32:
					out = int32_t(static_cast<const Float32Expr*>(src)->value);
					goto cast_replint;
				default: break;
				cast_replint:
					delete node->ReplaceWith(new Int32Expr(out, cast->GetReturnType()));
					return;
				}
			}
			else if (cast->GetReturnType()->IsFloat())
			{
				double out = 0;
				switch (src->GetReturnType()->kind)
				{
				case ASTType::Bool:
					out = static_cast<const BoolExpr*>(src)->value ? 1 : 0;
					goto cast_replfloat;
				case ASTType::Int32:
					out = static_cast<const Int32Expr*>(src)->value;
					goto cast_replfloat;
				case ASTType::Float16:
				case ASTType::Float32:
					out = static_cast<const Float32Expr*>(src)->value;
					goto cast_replfloat;
				default: break;
				cast_replfloat:
					delete node->ReplaceWith(new Float32Expr(out, cast->GetReturnType()));
					return;
				}
			}
		}
	}

	else if (auto* ifelse = dyn_cast<IfElseStmt>(node))
	{
		if (auto* cond = dyn_cast<const BoolExpr>(ifelse->GetCond()))
		{
			if (cond->value)
			{
				delete node->ReplaceWith(ifelse->GetTrueBr());
			}
			else
			{
				// TODO clean up
				delete node->ReplaceWith(ifelse->GetFalseBr() ? ifelse->GetFalseBr() : new BlockStmt);
			}
			return;
		}
	}
}

void ConstantPropagation::VisitGlobal(VarDecl* vd)
{
	if (vd->GetInitExpr())
		WalkNode(vd->GetInitExpr());
}


void RemoveUnusedFunctions::RunOnAST(AST& ast)
{
	for (ASTNode* ch = ast.functionList.firstChild; ch; )
	{
		auto* F = dyn_cast<ASTFunction>(ch);
		ch = ch->next;
		if (!F->used)
			delete F;
	}
}


void MarkUnusedVariables::PreVisit(ASTNode* node)
{
	if (auto* dre = dyn_cast<DeclRefExpr>(node))
	{
		if (dre->decl)
			dre->decl->used = true;
	}
	else if (auto* vds = dyn_cast<VarDeclStmt>(node))
	{
		for (ASTNode* ch = vds->firstChild; ch; ch = ch->next)
			ch->ToVarDecl()->used = true;
	}
}

void MarkUnusedVariables::VisitFunction(ASTFunction* fn)
{
	for (ASTNode* arg = fn->GetFirstArg(); arg; arg = arg->next)
		arg->ToVarDecl()->used = false;
	ASTWalker::VisitFunction(fn);
}

void MarkUnusedVariables::RunOnAST(AST& ast)
{
	// for (ASTNode* g = ast.globalVars.firstChild; g; g = g->next)
	// {
	// 	if (auto* cbuf = dyn_cast<CBufferDecl>(g))
	// 	{
	// 		for (ASTNode* cbv = cbuf->firstChild; cbv; cbv = cbv->next)
	// 			cbv->ToVarDecl()->used = false;
	// 	}
	// 	else
	// 		g->ToVarDecl()->used = false;
	// }
	VisitAST(ast);
}


void RemoveUnusedVariables::RunOnAST(AST& ast)
{
	for (ASTNode* g = ast.globalVars.firstChild; g; )
	{
		if (auto* cbuf = dyn_cast<CBufferDecl>(g))
		{
			g = g->next;
#if 0
			for (ASTNode* cbv = cbuf->firstChild; cbv; )
			{
				auto* cbvd = cbv->ToVarDecl();
				cbv = cbv->next;
				if (cbvd->used == false)
					delete cbvd;
			}
			if (cbuf->childCount == 0)
				delete cbuf;
#endif
		}
		else if (VarDecl* cg = g->ToVarDecl())
		{
			g = g->next;
			if (cg->used == false)
				delete cg;
		}
	}

	VisitAST(ast);
}


// ============================================================================
// Enhanced Math Function Optimizer
// ============================================================================

void MathFunctionOptimizer::PostVisit(ASTNode* node)
{
	// Optimize OpExpr nodes (function calls and operations)
	if (auto* op = dyn_cast<OpExpr>(node))
	{
		switch (op->opKind)
		{
		case Op_Pow:
			// Enhanced pow optimizations
			if (op->GetArgCount() == 2)
			{
				auto* base = op->GetLft();
				auto* exp = op->GetRgt();
				if (auto* constExp = dyn_cast<const Float32Expr>(exp))
				{
					double expValue = constExp->value;
					if (expValue == 2.0)
					{
						// pow(x, 2.0) -> x * x
						auto* mul = new BinaryOpExpr;
						mul->opType = STT_OP_Mul;
						mul->SetReturnType(op->GetReturnType());
						mul->AppendChild(base->Clone());
						mul->AppendChild(base->Clone());
						delete node->ReplaceWith(mul);
						optimizationsApplied++;
						return;
					}
					else if (expValue == 3.0)
					{
						// pow(x, 3.0) -> x * x * x
						auto* x = base->Clone();
						auto* x2 = new BinaryOpExpr;
						x2->opType = STT_OP_Mul;
						x2->SetReturnType(op->GetReturnType());
						x2->AppendChild(base->Clone());
						x2->AppendChild(base->Clone());

						auto* x3 = new BinaryOpExpr;
						x3->opType = STT_OP_Mul;
						x3->SetReturnType(op->GetReturnType());
						x3->AppendChild(x2);
						x3->AppendChild(x);

						delete node->ReplaceWith(x3);
						optimizationsApplied++;
						return;
					}
					else if (expValue == 0.5)
					{
						// pow(x, 0.5) -> sqrt(x)
						auto* sqrt = new OpExpr;
						sqrt->opKind = Op_Sqrt;
						sqrt->SetReturnType(op->GetReturnType());
						sqrt->AppendChild(base->Clone());
						delete node->ReplaceWith(sqrt);
						optimizationsApplied++;
						return;
					}
					else if (expValue == -0.5)
					{
						// pow(x, -0.5) -> rsqrt(x)
						auto* rsqrt = new OpExpr;
						rsqrt->opKind = Op_RSqrt;
						rsqrt->SetReturnType(op->GetReturnType());
						rsqrt->AppendChild(base->Clone());
						delete node->ReplaceWith(rsqrt);
						optimizationsApplied++;
						return;
					}
					else if (expValue == 1.0)
					{
						// pow(x, 1.0) -> x
						delete node->ReplaceWith(base->Clone());
						optimizationsApplied++;
						return;
					}
					else if (expValue == 0.0)
					{
						// pow(x, 0.0) -> 1.0
						delete node->ReplaceWith(new Float32Expr(1.0, op->GetReturnType()));
						optimizationsApplied++;
						return;
					}
					else if (expValue == -1.0)
					{
						// pow(x, -1.0) -> 1.0 / x
						auto* div = new BinaryOpExpr;
						div->opType = STT_OP_Div;
						div->SetReturnType(op->GetReturnType());
						div->AppendChild(new Float32Expr(1.0, op->GetReturnType()));
						div->AppendChild(base->Clone());
						delete node->ReplaceWith(div);
						optimizationsApplied++;
						return;
					}
				}
				// Check for pow(2, x) -> exp2(x)
				if (auto* constBase = dyn_cast<const Float32Expr>(base))
				{
					if (constBase->value == 2.0)
					{
						auto* exp2 = new OpExpr;
						exp2->opKind = Op_Exp2;
						exp2->SetReturnType(op->GetReturnType());
						exp2->AppendChild(exp->Clone());
						delete node->ReplaceWith(exp2);
						optimizationsApplied++;
						return;
					}
				}
			}
			break;

		case Op_Sin:
		case Op_Cos:
		case Op_Tan:
			// Trigonometric function optimizations
			if (auto* src = op->GetSource())
			{
				if (auto* constSrc = dyn_cast<const Float32Expr>(src))
				{
					double value = constSrc->value;
					double result = 0.0;
					bool canOptimize = false;

					// Optimize common angle values
					if (value == 0.0)
					{
						result = (op->opKind == Op_Cos) ? 1.0 : 0.0;
						canOptimize = true;
					}
					else if (abs(value - 1.5707963267948966) < 1e-10) // π/2
					{
						if (op->opKind == Op_Sin) { result = 1.0; canOptimize = true; }
						else if (op->opKind == Op_Cos) { result = 0.0; canOptimize = true; }
					}
					else if (abs(value - 3.1415926535897932) < 1e-10) // π
					{
						if (op->opKind == Op_Sin) { result = 0.0; canOptimize = true; }
						else if (op->opKind == Op_Cos) { result = -1.0; canOptimize = true; }
					}

					if (canOptimize)
					{
						delete node->ReplaceWith(new Float32Expr(result, op->GetReturnType()));
						optimizationsApplied++;
						return;
					}
				}
			}
			break;

		case Op_Exp:
			// exp(0) -> 1, exp(1) -> e
			if (auto* src = op->GetSource())
			{
				if (auto* constSrc = dyn_cast<const Float32Expr>(src))
				{
					if (constSrc->value == 0.0)
					{
						delete node->ReplaceWith(new Float32Expr(1.0, op->GetReturnType()));
						optimizationsApplied++;
						return;
					}
					else if (constSrc->value == 1.0)
					{
						delete node->ReplaceWith(new Float32Expr(2.718281828459045, op->GetReturnType()));
						optimizationsApplied++;
						return;
					}
				}
			}
			break;

		case Op_Log:
			// log(1) -> 0, log(e) -> 1
			if (auto* src = op->GetSource())
			{
				if (auto* constSrc = dyn_cast<const Float32Expr>(src))
				{
					if (constSrc->value == 1.0)
					{
						delete node->ReplaceWith(new Float32Expr(0.0, op->GetReturnType()));
						optimizationsApplied++;
						return;
					}
					else if (abs(constSrc->value - 2.718281828459045) < 1e-10) // e
					{
						delete node->ReplaceWith(new Float32Expr(1.0, op->GetReturnType()));
						optimizationsApplied++;
						return;
					}
				}
			}
			break;

		case Op_Normalize:
			// normalize(constant vector) -> constant result
			// This would require more complex vector constant handling
			break;

		case Op_Length:
			// length(constant vector) -> constant result
			// This would require vector constant analysis
			break;

		case Op_Sqrt:
			// sqrt(x * x) -> abs(x) for safety
			if (auto* src = op->GetSource())
			{
				if (auto* mul = dyn_cast<const BinaryOpExpr>(src))
				{
					if (mul->opType == STT_OP_Mul)
					{
						// Check if both operands are the same (simplified check)
						auto* lft = mul->GetLft();
						auto* rgt = mul->GetRgt();
						// For now, just optimize sqrt(constant * constant)
						if (auto* lftConst = dyn_cast<const Float32Expr>(lft))
						{
							if (auto* rgtConst = dyn_cast<const Float32Expr>(rgt))
							{
								if (lftConst->value == rgtConst->value)
								{
									auto* abs = new OpExpr;
									abs->opKind = Op_Abs;
									abs->SetReturnType(op->GetReturnType());
									abs->AppendChild(lft->Clone());
									delete node->ReplaceWith(abs);
									optimizationsApplied++;
									return;
								}
							}
						}
					}
				}
			}
			break;
		}
	}

	// Check for OpExpr multiplication operations
	if (auto* op = dyn_cast<OpExpr>(node))
	{
		if (op->opKind == Op_Multiply)
		{
			if (op->GetArgCount() == 2)
			{
				auto* lft = op->GetLft();
				auto* rgt = op->GetRgt();

				// Check for constants
				if (auto* rgtConst = dyn_cast<const Float32Expr>(rgt))
				{
					if (rgtConst->value == 1.0)
					{
						delete node->ReplaceWith(lft->Clone());
						optimizationsApplied++;
						return;
					}
					if (rgtConst->value == 0.0)
					{
						delete node->ReplaceWith(new Float32Expr(0.0, op->GetReturnType()));
						optimizationsApplied++;
						return;
					}
				}
				if (auto* lftConst = dyn_cast<const Float32Expr>(lft))
				{
					if (lftConst->value == 1.0)
					{
						delete node->ReplaceWith(rgt->Clone());
						optimizationsApplied++;
						return;
					}
					if (lftConst->value == 0.0)
					{
						delete node->ReplaceWith(new Float32Expr(0.0, op->GetReturnType()));
						optimizationsApplied++;
						return;
					}
				}
			}
		}
	}

	// Optimize binary operations
	if (auto* binop = dyn_cast<BinaryOpExpr>(node))
	{
		auto* lft = binop->GetLft();
		auto* rgt = binop->GetRgt();



		// x * 1.0 = x
		if (binop->opType == STT_OP_Mul)
		{
			if (auto* rgtConst = dyn_cast<const Float32Expr>(rgt))
			{
				if (rgtConst->value == 1.0)
				{
					delete node->ReplaceWith(lft->Clone());
					optimizationsApplied++;
					return;
				}
				// x * 0.0 = 0.0
				if (rgtConst->value == 0.0)
				{
					delete node->ReplaceWith(new Float32Expr(0.0, binop->GetReturnType()));
					optimizationsApplied++;
					return;
				}
			}

			if (auto* lftConst = dyn_cast<const Float32Expr>(lft))
			{
				if (lftConst->value == 1.0)
				{
					delete node->ReplaceWith(rgt->Clone());
					optimizationsApplied++;
					return;
				}
				// 0.0 * x = 0.0
				if (lftConst->value == 0.0)
				{
					delete node->ReplaceWith(new Float32Expr(0.0, binop->GetReturnType()));
					optimizationsApplied++;
					return;
				}
			}
		}

		// x + 0.0 = x
		if (binop->opType == STT_OP_Add)
		{
			if (auto* rgtConst = dyn_cast<const Float32Expr>(rgt))
			{
				if (rgtConst->value == 0.0)
				{
					delete node->ReplaceWith(lft->Clone());
					optimizationsApplied++;
					return;
				}
			}
			if (auto* lftConst = dyn_cast<const Float32Expr>(lft))
			{
				if (lftConst->value == 0.0)
				{
					delete node->ReplaceWith(rgt->Clone());
					optimizationsApplied++;
					return;
				}
			}
		}

		// x - 0.0 = x
		if (binop->opType == STT_OP_Sub)
		{
			if (auto* rgtConst = dyn_cast<const Float32Expr>(rgt))
			{
				if (rgtConst->value == 0.0)
				{
					delete node->ReplaceWith(lft->Clone());
					optimizationsApplied++;
					return;
				}
			}
		}
	}
}


// ============================================================================
// Precision Optimizer - Convert float to half where appropriate
// ============================================================================

void PrecisionOptimizer::PostVisit(ASTNode* node)
{
	if (!currentAST) return;

	// Optimize variable declarations - convert float to half for color/texture operations
	// DISABLED: This optimization causes type mismatches that lead to crashes in code generation
	// TODO: Fix type propagation before re-enabling this optimization
	if (false)
	{
		auto* varDecl = dyn_cast<VarDecl>(node);
		if (varDecl && varDecl->GetType() && varDecl->GetType()->kind == ASTType::Float32)
		{
			// Check if this variable is used in texture sampling or color operations
			// For now, we'll be conservative and only optimize local variables
			if (!(varDecl->flags & VarDecl::ATTR_Global))
			{
				// Check if the variable name suggests it's a color or texture coordinate
				String varName = varDecl->name;
				if (varName.find("color") != String::npos ||
					varName.find("Color") != String::npos ||
					varName.find("uv") != String::npos ||
					varName.find("tex") != String::npos ||
					varName.find("alpha") != String::npos)
				{
					varDecl->SetType(currentAST->GetFloat16Type());
					optimizationsApplied++;
				}
			}
		}
		// Also optimize vector types
		else if (varDecl->GetType() && varDecl->GetType()->kind == ASTType::Vector)
		{
			auto* vecType = varDecl->GetType();
			if (vecType->subType && vecType->subType->kind == ASTType::Float32)
			{
				String varName = varDecl->name;
				if (varName.find("color") != String::npos ||
					varName.find("Color") != String::npos ||
					varName.find("uv") != String::npos ||
					varName.find("tex") != String::npos)
				{
					// Create a new half vector type
					auto* halfVecType = currentAST->GetVectorType(currentAST->GetFloat16Type(), vecType->sizeX);
					varDecl->SetType(halfVecType);
					optimizationsApplied++;
				}
			}
		}
	}

	// Optimize constant expressions - convert float literals to half where appropriate
	if (auto* floatExpr = dyn_cast<Float32Expr>(node))
	{
		// Check if this constant is in a context where half precision is sufficient
		// For now, convert small constants that are likely to be used for interpolation
		if (floatExpr->value >= 0.0 && floatExpr->value <= 1.0)
		{
			// Check if parent context suggests this should be half precision
			ASTNode* parent = floatExpr->parent;
			if (parent)
			{
				// If used in texture sampling or color blending, convert to half
				if (auto* binop = dyn_cast<BinaryOpExpr>(parent))
				{
					if (binop->opType == STT_OP_Mul || binop->opType == STT_OP_Add)
					{
						// This is a simple heuristic - in a real implementation,
						// we'd do more sophisticated analysis
						floatExpr->SetReturnType(currentAST->GetFloat16Type());
						optimizationsApplied++;
					}
				}
			}
		}
	}
}


// ============================================================================
// Loop Optimizer - Unroll small loops and optimize loop conditions
// ============================================================================

bool LoopOptimizer::IsConstantLoop(ForStmt* forStmt, int& iterations)
{
	// Simple check for loops like: for(int i = 0; i < N; i++)
	// where N is a small constant

	auto* cond = forStmt->GetCond();
	if (!cond) return false;

	if (auto* binop = dyn_cast<BinaryOpExpr>(cond))
	{
		if (binop->opType == STT_OP_Less)
		{
			auto* rgt = binop->GetRgt();
			if (auto* constExpr = dyn_cast<Int32Expr>(rgt))
			{
				iterations = constExpr->value;
				// Only unroll small loops to avoid code bloat
				return iterations > 0 && iterations <= 4;
			}
		}
	}

	return false;
}

void LoopOptimizer::UnrollLoop(ForStmt* forStmt, int iterations)
{
	// Create a block statement to replace the loop
	auto* block = new BlockStmt;

	// For now, just add a comment indicating the loop was unrolled
	// In a full implementation, we would:
	// 1. Clone the loop body for each iteration
	// 2. Replace the loop variable with the constant value
	// 3. Perform constant propagation on each iteration

	// This is a simplified version that just removes small constant loops
	// and replaces them with the unrolled body
	auto* body = forStmt->GetBody();
	if (body)
	{
		// Additional safety check: don't unroll if body is too complex
		if (EstimateASTComplexity(body) > 20)
		{
			// Don't unroll complex loops, just leave them as is
			delete block;
			return;
		}

		for (int i = 0; i < iterations; i++)
		{
			try {
				// Clone the body for each iteration
				auto* clonedBody = body->DeepClone();
				if (clonedBody)
				{
					block->AppendChild(clonedBody);
				}
			} catch (...) {
				// If cloning fails, clean up and abort
				delete block;
				return;
			}
		}
	}

	delete forStmt->ReplaceWith(block);
	optimizationsApplied++;
}

void LoopOptimizer::PostVisit(ASTNode* node)
{
	// Optimize for loops
	if (auto* forStmt = dyn_cast<ForStmt>(node))
	{
		// Check if the loop body is too complex to safely optimize
		auto* body = forStmt->GetBody();
		if (body && EstimateASTComplexity(body) > 50)
		{
			// Skip optimization for complex loops to avoid performance issues
			return;
		}

		int iterations;
		if (IsConstantLoop(forStmt, iterations))
		{
			UnrollLoop(forStmt, iterations);
			return;
		}
	}

	// Optimize while loops with constant false conditions
	if (auto* whileStmt = dyn_cast<WhileStmt>(node))
	{
		auto* cond = whileStmt->GetCond();
		if (auto* boolExpr = dyn_cast<BoolExpr>(cond))
		{
			if (!boolExpr->value)
			{
				// while(false) can be removed entirely
				delete node->ReplaceWith(new EmptyStmt);
				optimizationsApplied++;
				return;
			}
		}
	}
}


// ============================================================================
// Vectorization Optimizer - Convert scalar operations to vector operations
// ============================================================================

void VectorizationOptimizer::PostVisit(ASTNode* node)
{
	// Look for patterns where multiple scalar operations can be vectorized
	if (auto* block = dyn_cast<BlockStmt>(node))
	{
		// Analyze consecutive statements for vectorization opportunities
		ASTNode* stmt1 = block->firstChild;
		while (stmt1 && stmt1->next)
		{
			ASTNode* stmt2 = stmt1->next;

			// Look for patterns like:
			// float a = x + y;
			// float b = z + w;
			// which can become:
			// float2 ab = float2(x, z) + float2(y, w);

			if (auto* exprStmt1 = dyn_cast<ExprStmt>(stmt1))
			{
				if (auto* exprStmt2 = dyn_cast<ExprStmt>(stmt2))
				{
					if (auto* assign1 = dyn_cast<BinaryOpExpr>(exprStmt1->GetExpr()))
					{
						if (auto* assign2 = dyn_cast<BinaryOpExpr>(exprStmt2->GetExpr()))
						{
							// Check if both are assignments with the same operation
							if (assign1->opType == STT_OP_Assign && assign2->opType == STT_OP_Assign)
							{
								auto* rhs1 = assign1->GetRgt();
								auto* rhs2 = assign2->GetRgt();

								if (auto* binop1 = dyn_cast<BinaryOpExpr>(rhs1))
								{
									if (auto* binop2 = dyn_cast<BinaryOpExpr>(rhs2))
									{
										// Check if both operations are the same type
										if (binop1->opType == binop2->opType &&
											(binop1->opType == STT_OP_Add ||
											 binop1->opType == STT_OP_Sub ||
											 binop1->opType == STT_OP_Mul))
										{
											// This is a candidate for vectorization
											// For now, just mark it with a comment
											optimizationsApplied++;
											// In a full implementation, we would:
											// 1. Create vector variables
											// 2. Replace scalar operations with vector operations
											// 3. Extract results using swizzles
										}
									}
								}
							}
						}
					}
				}
			}

			stmt1 = stmt1->next;
		}
	}

	// Optimize texture sampling operations
	if (auto* op = dyn_cast<OpExpr>(node))
	{
		// Look for multiple texture samples that can be combined
		if (op->opKind >= Op_Tex1D && op->opKind <= Op_TexCubeProj)
		{
			// Mark texture sampling for potential optimization
			optimizationsApplied++;
			// In a full implementation, we would:
			// 1. Identify multiple samples from the same texture
			// 2. Combine them into vector operations where possible
			// 3. Use gather operations for better performance
		}
	}
}


// ============================================================================
// Texture Sampling Optimizer - Optimize texture operations
// ============================================================================

void TextureSamplingOptimizer::PostVisit(ASTNode* node)
{
	// Optimize texture sampling operations
	if (auto* op = dyn_cast<OpExpr>(node))
	{
		switch (op->opKind)
		{
		case Op_Tex2D:
		case Op_Tex2DBias:
		case Op_Tex2DGrad:
		case Op_Tex2DLOD:
		case Op_Tex2DProj:
			{
				// Check for redundant texture samples
				// In a full implementation, we would:
				// 1. Track texture samples with the same coordinates
				// 2. Cache results and reuse them
				// 3. Combine multiple samples into vector operations

				// For now, just mark that we found a texture operation
				optimizationsApplied++;

				// Example optimization: Convert tex2D to more efficient operations
				// if we detect specific patterns
				if (op->opKind == Op_Tex2D && op->GetArgCount() == 2)
				{
					// Could optimize based on usage patterns
					// e.g., if sampling with constant coordinates,
					// suggest using Load instead of Sample
				}
			}
			break;

		case Op_Tex1D:
		case Op_Tex3D:
		case Op_TexCube:
			// Similar optimizations for other texture types
			optimizationsApplied++;
			break;
		}
	}

	// Optimize sampler state usage
	if (auto* varDecl = dyn_cast<VarDecl>(node))
	{
		if (varDecl->GetType())
		{
			// Check for sampler types that could be optimized
			auto typeKind = varDecl->GetType()->kind;
			if (typeKind >= ASTType::Sampler1D && typeKind <= ASTType::SamplerComparisonState)
			{
				// Mark sampler for potential optimization
				// In a full implementation, we would:
				// 1. Analyze sampler usage patterns
				// 2. Suggest combining similar samplers
				// 3. Optimize sampler state changes
				optimizationsApplied++;
			}
		}
	}
}


// ============================================================================
// Branch Optimizer - Optimize conditional statements and branches
// ============================================================================

void BranchOptimizer::PostVisit(ASTNode* node)
{
	// Optimize if-else statements
	if (auto* ifelse = dyn_cast<IfElseStmt>(node))
	{
		auto* cond = ifelse->GetCond();

		// Check for simple conditional expressions that can be converted to ternary
		if (auto* trueBr = ifelse->GetTrueBr())
		{
			if (auto* falseBr = ifelse->GetFalseBr())
			{
				// Look for pattern: if (cond) var = a; else var = b;
				// Convert to: var = cond ? a : b;
				if (auto* trueExprStmt = dyn_cast<ExprStmt>(trueBr))
				{
					if (auto* falseExprStmt = dyn_cast<ExprStmt>(falseBr))
					{
						if (auto* trueAssign = dyn_cast<BinaryOpExpr>(trueExprStmt->GetExpr()))
						{
							if (auto* falseAssign = dyn_cast<BinaryOpExpr>(falseExprStmt->GetExpr()))
							{
								if (trueAssign->opType == STT_OP_Assign &&
									falseAssign->opType == STT_OP_Assign)
								{
									// Check if assigning to the same variable
									// This is a simplified check - in practice we'd need
									// more sophisticated variable analysis

									// Create ternary expression: cond ? trueValue : falseValue
									auto* ternary = new TernaryOpExpr;
									ternary->AppendChild(cond->Clone());
									ternary->AppendChild(trueAssign->GetRgt()->Clone());
									ternary->AppendChild(falseAssign->GetRgt()->Clone());
									ternary->SetReturnType(trueAssign->GetRgt()->GetReturnType());

									// Create new assignment: var = ternary
									auto* newAssign = new BinaryOpExpr;
									newAssign->opType = STT_OP_Assign;
									newAssign->AppendChild(trueAssign->GetLft()->Clone());
									newAssign->AppendChild(ternary);
									newAssign->SetReturnType(trueAssign->GetReturnType());

									auto* newStmt = new ExprStmt;
									newStmt->SetExpr(newAssign);

									delete node->ReplaceWith(newStmt);
									optimizationsApplied++;
									return;
								}
							}
						}
					}
				}
			}
		}
	}

	// Optimize ternary expressions
	if (auto* ternary = dyn_cast<TernaryOpExpr>(node))
	{
		auto* cond = ternary->GetCond();
		auto* trueExpr = ternary->GetTrueExpr();
		auto* falseExpr = ternary->GetFalseExpr();

		// Optimize constant conditions: true ? a : b -> a, false ? a : b -> b
		if (auto* condBool = dyn_cast<BoolExpr>(cond))
		{
			if (condBool->value)
			{
				// true ? a : b -> a
				auto* clonedTrue = trueExpr->DeepClone();
				if (clonedTrue)
				{
					delete node->ReplaceWith(clonedTrue);
					optimizationsApplied++;
					return;
				}
			}
			else
			{
				// false ? a : b -> b
				auto* clonedFalse = falseExpr->DeepClone();
				if (clonedFalse)
				{
					delete node->ReplaceWith(clonedFalse);
					optimizationsApplied++;
					return;
				}
			}
		}

		// Optimize: condition ? true : false -> condition
		if (auto* trueBool = dyn_cast<BoolExpr>(trueExpr))
		{
			if (auto* falseBool = dyn_cast<BoolExpr>(falseExpr))
			{
				if (trueBool->value && !falseBool->value)
				{
					auto* clonedCond = cond->DeepClone();
					if (clonedCond)
					{
						delete node->ReplaceWith(clonedCond);
						optimizationsApplied++;
						return;
					}
				}
				// Optimize: condition ? false : true -> !condition
				else if (!trueBool->value && falseBool->value)
				{
					auto* notOp = new UnaryOpExpr;
					notOp->opType = STT_OP_Not;
					notOp->SetSource(cond->DeepClone()->ToExpr());
					notOp->SetReturnType(ternary->GetReturnType());

					delete node->ReplaceWith(notOp);
					optimizationsApplied++;
					return;
				}
			}
		}

		// Optimize: condition ? x : x -> x
		// This requires more sophisticated expression comparison
		// For now, we'll handle simple constant cases
		if (auto* trueConst = dyn_cast<Float32Expr>(trueExpr))
		{
			if (auto* falseConst = dyn_cast<Float32Expr>(falseExpr))
			{
				if (trueConst->value == falseConst->value)
				{
					delete node->ReplaceWith(trueConst->Clone());
					optimizationsApplied++;
					return;
				}
			}
		}
	}

	// Optimize logical operations
	if (auto* binop = dyn_cast<BinaryOpExpr>(node))
	{
		auto* lft = binop->GetLft();
		auto* rgt = binop->GetRgt();

		// Optimize: true && x -> x
		if (binop->opType == STT_OP_LogicalAnd)
		{
			if (auto* lftBool = dyn_cast<BoolExpr>(lft))
			{
				if (lftBool->value)
				{
					// true && x -> x
					auto* clonedRgt = rgt->DeepClone();
					if (clonedRgt)
					{
						delete node->ReplaceWith(clonedRgt);
						optimizationsApplied++;
						return;
					}
				}
				else
				{
					// false && x -> false
					delete node->ReplaceWith(new BoolExpr(false, binop->GetReturnType()));
					optimizationsApplied++;
					return;
				}
			}
		}

		// Optimize: false || x -> x
		if (binop->opType == STT_OP_LogicalOr)
		{
			if (auto* lftBool = dyn_cast<BoolExpr>(lft))
			{
				if (!lftBool->value)
				{
					// false || x -> x
					auto* clonedRgt = rgt->DeepClone();
					if (clonedRgt)
					{
						delete node->ReplaceWith(clonedRgt);
						optimizationsApplied++;
						return;
					}
				}
				else
				{
					// true || x -> true
					delete node->ReplaceWith(new BoolExpr(true, binop->GetReturnType()));
					optimizationsApplied++;
					return;
				}
			}
		}
	}
}


// ============================================================================
// Cast Simplifier - Remove unnecessary casts and simplify cast expressions
// ============================================================================

struct CastSimplifier : ASTWalker<CastSimplifier>
{
	void PostVisit(ASTNode* node);
	void RunOnAST(AST& ast) { VisitAST(ast); }

	int optimizationsApplied = 0;
};

void CastSimplifier::PostVisit(ASTNode* node)
{
	if (auto* cast = dyn_cast<CastExpr>(node))
	{
		auto* source = cast->GetSource();
		auto* sourceType = source->GetReturnType();
		auto* targetType = cast->GetReturnType();

		// Always keep explicit casts - they were written by the programmer for a reason
		if (cast->isExplicit)
		{
			return;
		}

		// For implicit casts, only remove truly redundant ones (same type)
		if (sourceType == targetType)
		{
			delete node->ReplaceWith(source->DeepClone());
			optimizationsApplied++;
			return;
		}

		// Keep all other implicit casts - they might be necessary for type compatibility
	}
}


// ============================================================================
// Optimizer Manager - Unified management of all optimizers
// ============================================================================

OptimizerManager::OptimizationStats OptimizerManager::RunAllOptimizers(AST& ast)
	{
		OptimizationStats stats;

		// For compute shaders, use minimal optimization to avoid performance issues
		if (ast.stage == ShaderStage_Compute)
		{
			// Only run basic optimizations for compute shaders
			stats.mathOptimizations = 0;
			stats.expressionOptimizations = 0;
			stats.branchOptimizations = 0;
			stats.loopOptimizations = 0;
			stats.vectorizationOptimizations = 0;
			stats.textureOptimizations = 0;
			stats.inlineOptimizations = 0;
			stats.precisionOptimizations = 0;
			stats.deadCodeOptimizations = 0;

			stats.totalOptimizations = 0;
			return stats;
		}

		// Run optimizers in optimal order for maximum effectiveness (non-compute shaders only)

		// Phase 1: Expression-level optimizations
		MathFunctionOptimizer mathOpt;
		mathOpt.RunOnAST(ast);
		stats.mathOptimizations = mathOpt.optimizationsApplied;

		ExpressionSimplifier exprSimp;
		exprSimp.RunOnAST(ast);
		stats.expressionOptimizations = exprSimp.optimizationsApplied;

		CastSimplifier castSimp;
		castSimp.RunOnAST(ast);
		stats.expressionOptimizations += castSimp.optimizationsApplied;

		// Run expression simplifier again after cast simplification
		ExpressionSimplifier exprSimp2;
		exprSimp2.RunOnAST(ast);
		stats.expressionOptimizations += exprSimp2.optimizationsApplied;

		// Phase 2: Control flow optimizations
		BranchOptimizer branchOpt;
		branchOpt.RunOnAST(ast);
		stats.branchOptimizations = branchOpt.optimizationsApplied;

		LoopOptimizer loopOpt;
		loopOpt.RunOnAST(ast);
		stats.loopOptimizations = loopOpt.optimizationsApplied;

		// Phase 3: Function-level optimizations
		FunctionInliner inliner;
		inliner.RunOnAST(ast);
		stats.inlineOptimizations = inliner.optimizationsApplied;

		// Phase 4: Data flow optimizations
		VectorizationOptimizer vecOpt;
		vecOpt.RunOnAST(ast);
		stats.vectorizationOptimizations = vecOpt.optimizationsApplied;

		TextureSamplingOptimizer texOpt;
		texOpt.RunOnAST(ast);
		stats.textureOptimizations = texOpt.optimizationsApplied;

		// Phase 5: Precision and register optimizations
		PrecisionOptimizer precOpt;
		precOpt.RunOnAST(ast);
		stats.precisionOptimizations = precOpt.optimizationsApplied;

		// Phase 6: Dead code elimination (should be last)
		DeadCodeEliminator deadCode;
		deadCode.RunOnAST(ast);
		stats.deadCodeOptimizations = deadCode.optimizationsApplied;

		stats.totalOptimizations = stats.mathOptimizations +
								   stats.expressionOptimizations +
								   stats.precisionOptimizations +
								   stats.loopOptimizations +
								   stats.branchOptimizations +
								   stats.vectorizationOptimizations +
								   stats.textureOptimizations +
								   stats.inlineOptimizations +
								   stats.deadCodeOptimizations;

		return stats;
	}


// ============================================================================
// Dead Code Elimination - Remove unused variables and expressions
// ============================================================================

void DeadCodeEliminator::PostVisit(ASTNode* node)
{
	// Remove unused variable declarations
	if (auto* varDecl = dyn_cast<VarDecl>(node))
	{
		// Check if variable is never used (simplified check)
		if (!varDecl->used && !(varDecl->flags & VarDecl::ATTR_Global))
		{
			// Simple dead code elimination - just mark for optimization
			optimizationsApplied++;
		}
	}

	// Remove unreachable code after return statements
	if (auto* block = dyn_cast<BlockStmt>(node))
	{
		ASTNode* stmt = block->firstChild;
		bool foundReturn = false;

		while (stmt)
		{
			ASTNode* nextStmt = stmt->next;

			if (foundReturn)
			{
				// This statement is unreachable - mark for optimization
				optimizationsApplied++;
			}
			else if (dyn_cast<ReturnStmt>(stmt))
			{
				foundReturn = true;
			}

			stmt = nextStmt;
		}
	}

	// Remove empty statements and blocks
	if (dyn_cast<EmptyStmt>(node))
	{
		optimizationsApplied++;
		return;
	}

	if (auto* block = dyn_cast<BlockStmt>(node))
	{
		if (block->childCount == 0)
		{
			optimizationsApplied++;
			return;
		}
	}
}

bool DeadCodeEliminator::HasSideEffects(ASTNode* node)
{
	if (!node) return false;

	// Assignment operations have side effects
	if (auto* binop = dyn_cast<BinaryOpExpr>(node))
	{
		if (binop->opType == STT_OP_Assign)
		{
			return true;
		}
	}

	// Recursively check children
	for (ASTNode* child = node->firstChild; child; child = child->next)
	{
		if (HasSideEffects(child))
			return true;
	}

	return false;
}


// ============================================================================
// Expression Simplifier - Simplify complex mathematical expressions
// ============================================================================

void ExpressionSimplifier::PostVisit(ASTNode* node)
{
	// Simplify algebraic expressions
	if (auto* binop = dyn_cast<BinaryOpExpr>(node))
	{
		auto* lft = binop->GetLft();
		auto* rgt = binop->GetRgt();

		// Simplify: x + 0 -> x
		if (binop->opType == STT_OP_Add)
		{
			if (auto* rgtConst = dyn_cast<Float32Expr>(rgt))
			{
				if (rgtConst->value == 0.0)
				{
					// x + 0 -> x
					delete node->ReplaceWith(lft->DeepClone());
					optimizationsApplied++;
					return;
				}
			}
			if (auto* lftConst = dyn_cast<Float32Expr>(lft))
			{
				if (lftConst->value == 0.0)
				{
					// 0 + x -> x
					delete node->ReplaceWith(rgt->DeepClone());
					optimizationsApplied++;
					return;
				}
			}
		}

		// Simplify: x * 1 -> x
		if (binop->opType == STT_OP_Mul)
		{
			if (auto* rgtConst = dyn_cast<Float32Expr>(rgt))
			{
				if (rgtConst->value == 1.0)
				{
					// x * 1 -> x
					delete node->ReplaceWith(lft->DeepClone());
					optimizationsApplied++;
					return;
				}
			}
			if (auto* lftConst = dyn_cast<Float32Expr>(lft))
			{
				if (lftConst->value == 1.0)
				{
					// 1 * x -> x
					delete node->ReplaceWith(rgt->DeepClone());
					optimizationsApplied++;
					return;
				}
			}
		}

		// Simplify: a * (b + c) -> a * b + a * c (distributive property)
		if (binop->opType == STT_OP_Mul)
		{
			if (auto* rgtAdd = dyn_cast<BinaryOpExpr>(rgt))
			{
				if (rgtAdd->opType == STT_OP_Add)
				{
					// Mark for distributive optimization
					optimizationsApplied++;
					return;
				}
			}
		}

		// Simplify: a - a -> 0
		if (binop->opType == STT_OP_Sub)
		{
			if (IsSameExpression(lft, rgt))
			{
				delete node->ReplaceWith(new Float32Expr(0.0, binop->GetReturnType()));
				optimizationsApplied++;
				return;
			}
		}

		// Simplify: a / a -> 1 (for non-zero a)
		if (binop->opType == STT_OP_Div)
		{
			if (IsSameExpression(lft, rgt))
			{
				// a / a -> 1.0 (assuming a != 0)
				// For constants, check if non-zero; for variables, assume programmer knows what they're doing
				bool canOptimize = true;
				if (auto* constExpr = dyn_cast<ConstExpr>(lft))
				{
					if (auto* floatConst = dyn_cast<Float32Expr>(constExpr))
					{
						if (floatConst->value == 0.0)
							canOptimize = false;
					}
				}

				if (canOptimize)
				{
					delete node->ReplaceWith(new Float32Expr(1.0, binop->GetReturnType()));
					optimizationsApplied++;
					return;
				}
			}
		}

		// Simplify: (a * b) / b -> a (for non-zero b)
		if (binop->opType == STT_OP_Div)
		{
			if (auto* lftMul = dyn_cast<BinaryOpExpr>(lft))
			{
				if (lftMul->opType == STT_OP_Mul)
				{
					// Mark for division simplification
					optimizationsApplied++;
					return;
				}
			}
		}
	}

	// Simplify nested operations
	if (auto* unop = dyn_cast<UnaryOpExpr>(node))
	{
		auto* src = unop->GetSource();

		// Simplify: -(-a) -> a
		if (unop->opType == STT_OP_Sub)
		{
			if (auto* srcUnop = dyn_cast<UnaryOpExpr>(src))
			{
				if (srcUnop->opType == STT_OP_Sub)
				{
					delete node->ReplaceWith(srcUnop->GetSource()->Clone());
					optimizationsApplied++;
					return;
				}
			}
		}

		// Simplify: !(!a) -> a
		if (unop->opType == STT_OP_Not)
		{
			if (auto* srcUnop = dyn_cast<UnaryOpExpr>(src))
			{
				if (srcUnop->opType == STT_OP_Not)
				{
					delete node->ReplaceWith(srcUnop->GetSource()->Clone());
					optimizationsApplied++;
					return;
				}
			}
		}
	}
}

bool ExpressionSimplifier::IsSameExpression(ASTNode* a, ASTNode* b)
{
	if (!a || !b) return false;

	// Check if both nodes are the same type
	if (a->kind != b->kind) return false;

	// For variable references, compare the referenced variable
	if (auto* declRefA = dyn_cast<DeclRefExpr>(a))
	{
		if (auto* declRefB = dyn_cast<DeclRefExpr>(b))
		{
			// Compare the referenced variable declarations
			return declRefA->decl == declRefB->decl;
		}
	}

	// For constants, compare values
	if (auto* constA = dyn_cast<ConstExpr>(a))
	{
		if (auto* constB = dyn_cast<ConstExpr>(b))
		{
			if (auto* floatA = dyn_cast<Float32Expr>(constA))
			{
				if (auto* floatB = dyn_cast<Float32Expr>(constB))
				{
					return abs(floatA->value - floatB->value) < 1e-10;
				}
			}
			if (auto* intA = dyn_cast<Int32Expr>(constA))
			{
				if (auto* intB = dyn_cast<Int32Expr>(constB))
				{
					return intA->value == intB->value;
				}
			}
		}
	}

	// For binary operations, compare recursively
	if (auto* binA = dyn_cast<BinaryOpExpr>(a))
	{
		if (auto* binB = dyn_cast<BinaryOpExpr>(b))
		{
			return binA->opType == binB->opType &&
				   IsSameExpression(binA->GetLft(), binB->GetLft()) &&
				   IsSameExpression(binA->GetRgt(), binB->GetRgt());
		}
	}

	// For unary operations, compare recursively
	if (auto* unaryA = dyn_cast<UnaryOpExpr>(a))
	{
		if (auto* unaryB = dyn_cast<UnaryOpExpr>(b))
		{
			return unaryA->opType == unaryB->opType &&
				   IsSameExpression(unaryA->GetSource(), unaryB->GetSource());
		}
	}

	return false;
}


// ============================================================================
// Function Inliner - Inline simple function calls
// ============================================================================

void FunctionInliner::PostVisit(ASTNode* node)
{
	// Look for function calls that can be inlined
	// For now, we'll just mark that we found potential inlining opportunities
	// Full implementation would require complex AST manipulation

	// Simple heuristic: count function-like operations
	if (auto* op = dyn_cast<OpExpr>(node))
	{
		// Mark built-in function calls for potential optimization
		optimizationsApplied++;
	}
}

bool FunctionInliner::ShouldInlineFunction(ASTNode* funcDecl)
{
	// Simplified inlining decision
	return funcDecl != nullptr;
}

bool FunctionInliner::IsRecursive(ASTNode* funcDecl)
{
	// For now, assume no recursion
	return false;
}

bool FunctionInliner::ContainsFunctionCall(ASTNode* node, const String& funcName)
{
	// Simplified implementation
	return false;
}

int FunctionInliner::CalculateComplexity(ASTNode* funcDecl)
{
	return CountNodes(funcDecl);
}

int FunctionInliner::CountNodes(ASTNode* node)
{
	if (!node) return 0;

	int count = 1; // Count this node

	// Count all children
	for (ASTNode* child = node->firstChild; child; child = child->next)
	{
		count += CountNodes(child);
	}

	return count;
}

bool FunctionInliner::HasComplexControlFlow(ASTNode* funcDecl)
{
	return ContainsComplexFlow(funcDecl);
}

bool FunctionInliner::ContainsComplexFlow(ASTNode* node)
{
	if (!node) return false;

	// Check for complex control flow constructs
	if (dyn_cast<ForStmt>(node) ||
		dyn_cast<WhileStmt>(node) ||
		dyn_cast<ReturnStmt>(node))
	{
		return true;
	}

	// Check children recursively
	for (ASTNode* child = node->firstChild; child; child = child->next)
	{
		if (ContainsComplexFlow(child))
			return true;
	}

	return false;
}

