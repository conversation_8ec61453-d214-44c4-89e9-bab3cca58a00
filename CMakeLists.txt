cmake_minimum_required(VERSION 3.16)
project(hlsloptconv VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W3 /MDd /GR- /D_DEBUG /Zi)
    add_compile_definitions(
        HOC_MALLOC_REPLACEMENT=chkmalloc 
        HOC_FREE_REPLACEMENT=chkfree
        _CRT_SECURE_NO_WARNINGS
    )
else()
    add_compile_options(-Wall -Wextra -g)
endif()

# Core library sources
set(CORE_SOURCES
    src/common.cpp
    src/hlslparser.cpp
    src/compiler.cpp
    src/optimizer.cpp
    src/generator.cpp
)

# Core library headers
set(CORE_HEADERS
    src/common.hpp
    src/hlslparser.hpp
    src/compiler.hpp
    src/hlsloptconv.h
)

# Create core library
add_library(hlsloptconv_core STATIC ${CORE_SOURCES} ${CORE_HEADERS})

# Include directories
target_include_directories(hlsloptconv_core PUBLIC src)

# hlsloptconv.exe - Command line tool
add_executable(hlsloptconv src/tools/cli.cpp)
target_link_libraries(hlsloptconv hlsloptconv_core)

# four.exe - Graphics demo (Windows only)
if(WIN32)
    add_executable(four src/tools/four.cpp)
    target_link_libraries(four 
        hlsloptconv_core
        user32
        gdi32
        msimg32
        d3d9
        d3d11
        d3dcompiler
        opengl32
    )
    
    # Set subsystem for four.exe (console app with main())
    if(MSVC)
        set_target_properties(four PROPERTIES
            LINK_FLAGS "/SUBSYSTEM:CONSOLE"
        )
    endif()
endif()

# Set output directories to bin/
set_target_properties(hlsloptconv PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_SOURCE_DIR}/bin
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_SOURCE_DIR}/bin
)

if(WIN32)
    set_target_properties(four PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin
        RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_SOURCE_DIR}/bin
        RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_SOURCE_DIR}/bin
    )
endif()

# Install targets
install(TARGETS hlsloptconv DESTINATION bin)
if(WIN32)
    install(TARGETS four DESTINATION bin)
endif()

# Copy shader files for four.exe
if(WIN32)
    # Copy existing test shader file
    configure_file(${CMAKE_SOURCE_DIR}/runtests/html5-shader.hlsl
                   ${CMAKE_BINARY_DIR}/bin/runtests/html5-shader.hlsl COPYONLY)
    # Copy test simple shader if it exists
    if(EXISTS ${CMAKE_SOURCE_DIR}/test_simple.hlsl)
        configure_file(${CMAKE_SOURCE_DIR}/test_simple.hlsl
                       ${CMAKE_BINARY_DIR}/bin/test_simple.hlsl COPYONLY)
    endif()
endif()

# Print build information
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
if(WIN32)
    message(STATUS "Building: hlsloptconv.exe, four.exe, example.exe")
else()
    message(STATUS "Building: hlsloptconv, example")
endif()

# Add example program for debugging
add_executable(example example.cpp)
target_link_libraries(example hlsloptconv_core)

if(WIN32)
    set_target_properties(example PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/bin
        RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_SOURCE_DIR}/bin
        RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_SOURCE_DIR}/bin
    )
endif()
