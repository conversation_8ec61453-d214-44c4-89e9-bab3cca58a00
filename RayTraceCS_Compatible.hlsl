struct Struct_VertexBuffer
{
    float3 Position_;
    float3 Normal;
    float3 Color;
    float4 Tangent;
    float2 UV;
    int MaterialID;
    int ShapeID;
};

struct Struct__MeshViewerCB
{
    float3 CameraPos;
    float _padding0;
    float4x4 InvViewProjMtx;
};

SamplerState samLinear : register(s0);
RWTexture2D<float4> Output : register(u0);
StructuredBuffer<Struct_VertexBuffer> VertexBuffer : register(t1);
ConstantBuffer<Struct__MeshViewerCB> _MeshViewerCB : register(b0);

// Define some constants
#define M_PI 3.1415926535897932384626433832795
#define M_INV_PI 0.31830988618379067153776752674503

// PBR material functions
float3 fresnelSchlick(float cosTheta, float3 F0)
{
    return F0 + (1.0 - F0) * pow(1.0 - cosTheta, 5.0);
}

float D_GGX(float NoH, float alpha)
{
    float alpha2 = alpha * alpha;
    float d = (NoH * NoH) * (alpha2 - 1.0) + 1.0;
    return alpha2 * M_INV_PI / (d * d);
}

float G1_GGX_Schlick(float NoV, float alpha) 
{
    float k = alpha / 2.0;
    return max(NoV, 0.001) / (NoV * (1.0 - k) + k);
}

float G_Smith(float NoV, float NoL, float alpha) 
{
    return G1_GGX_Schlick(NoL, alpha) * G1_GGX_Schlick(NoV, alpha);
}

float3 microfacetBRDF(float3 L, float3 V, float3 N, float metallic, float roughness, float3 baseColor, float specularlevel)
{
    float3 H = normalize(V + L);

    float NoV = clamp(dot(N, V), 0.0, 1.0);
    float NoL = clamp(dot(N, L), 0.0, 1.0);
    float NoH = clamp(dot(N, H), 0.0, 1.0);
    float VoH = clamp(dot(V, H), 0.0, 1.0);
    
    float3 f0 = 0.16 * (specularlevel * specularlevel);
    f0 = lerp(f0, baseColor, metallic);

    float alpha = roughness * roughness;

    float3 F = fresnelSchlick(VoH, f0);
    float D = D_GGX(NoH, alpha);
    float G = G_Smith(NoV, NoL, alpha);

    float3 specular = (F * D * G) / (4.0 * max(NoV, 0.001) * max(NoL, 0.001));

    float3 rhoD = baseColor;
    rhoD *= 1.0 - F;
    rhoD *= (1.0 - metallic);

    float3 diffuse = rhoD * M_INV_PI;

    return diffuse + specular;
}

// Simple ray-sphere intersection for demonstration
bool intersectSphere(float3 rayOrigin, float3 rayDir, float3 sphereCenter, float sphereRadius, out float t)
{
    float3 oc = rayOrigin - sphereCenter;
    float a = dot(rayDir, rayDir);
    float b = 2.0 * dot(oc, rayDir);
    float c = dot(oc, oc) - sphereRadius * sphereRadius;
    float discriminant = b * b - 4 * a * c;
    
    if (discriminant < 0)
        return false;
        
    t = (-b - sqrt(discriminant)) / (2.0 * a);
    return t > 0;
}

[numthreads(8, 8, 1)]
void main(uint3 DTid : SV_DispatchThreadID)
{
    uint2 px = DTid.xy;
    uint w, h;
    Output.GetDimensions(w, h);
    float2 dimensions = float2(w, h);

    float2 screenPos = (float2(px) + 0.5f) / dimensions * 2.0 - 1.0;
    screenPos.y = -screenPos.y;

    float4 world = mul(float4(screenPos, 0.99f, 1), _MeshViewerCB.InvViewProjMtx);
    world.xyz /= world.w;

    float3 rayOrigin = _MeshViewerCB.CameraPos;
    float3 rayDirection = normalize(world.xyz - rayOrigin);

    // Simple sphere intersection instead of raytracing
    float3 sphereCenter = float3(0, 0, 0);
    float sphereRadius = 1.0;
    float t;
    
    if (intersectSphere(rayOrigin, rayDirection, sphereCenter, sphereRadius, t))
    {
        // Calculate hit point and normal
        float3 hitPoint = rayOrigin + rayDirection * t;
        float3 normal = normalize(hitPoint - sphereCenter);

        // Light parameters
        float3 lightPosition = float3(2.0f, 3.0f, 2.0f);
        float3 lightColor = float3(1.0f, 1.0f, 1.0f);
        float lightIntensity = 2.0f;

        // Material parameters
        float3 basecolor = float3(0.8f, 0.8f, 0.8f);
        float roughness = 0.4f;
        float specularlevel = 0.5f;
        float metallic = 0.0f;

        // Calculate lighting
        float3 lightDirection = normalize(lightPosition - hitPoint);
        float distanceToLight = length(lightPosition - hitPoint);

        // Distance attenuation
        float attenuation = 1.0f / (1.0f + distanceToLight * 0.1f);
        float3 lightPerpendicularIrradiance = lightIntensity * lightColor * attenuation;

        // View direction
        float3 viewDirection = normalize(_MeshViewerCB.CameraPos - hitPoint);

        // PBR lighting calculation
        float3 irradiance = max(dot(lightDirection, normal), 0.0) * lightPerpendicularIrradiance;
        float3 brdf = microfacetBRDF(lightDirection, viewDirection, normal, metallic, roughness, basecolor, specularlevel);
        float3 radiance = irradiance * brdf;

        // Ambient lighting
        radiance += float3(0.02f, 0.02f, 0.02f);
        Output[px] = float4(radiance, 1.0f);
    }
    else
    {
        Output[px] = float4(0.0f, 0.0f, 0.0f, 0.0f);
    }
}
