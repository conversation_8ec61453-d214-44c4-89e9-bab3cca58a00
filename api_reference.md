# API Reference

This document provides a comprehensive reference for the hlsloptconv API and command-line interface.

## Command Line Interface

### Basic Syntax

```bash
hlsloptconv [options] input_file
```

### Required Parameters

- `-f, --format <format>` - Output format (see supported formats below)
- `-s, --stage <stage>` - Shader stage: `vertex`, `pixel`

### Optional Parameters

#### Basic Options
- `-i, --input <format>` - Input format (`auto`, `hlsl`, `glsl`, `glsl_es`)
- `-o, --output <file>` - Output file path (default: stdout)
- `-P, --stdout` - Pretty-print output to console with syntax highlighting
- `-e, --entrypoint <name>` - Entry point function name (default: `main`)

#### Advanced Options
- `-d, --dump` - Dump Abstract Syntax Tree (AST) for debugging
- `-O, --optimize <level>` - Optimization level 0-4 (default: 2)
- `--stats` - Show detailed optimization statistics
- `--backup` - Create backup of output file before overwriting
- `--fast-math` - Enable aggressive math optimizations
- `--target <platform>` - Target platform: `generic`, `mobile`, `desktop`, `console`

#### Preprocessor Options
- `-D<name>[=<value>]` - Define preprocessor macro

### Supported Formats

#### Input Formats
- `auto` - Auto-detect based on file extension and content (default)
- `hlsl` - HLSL input (all shader models)
#### Output Formats

##### HLSL Targets
- `hlsl_sm3` - HLSL Shader Model 3.0 (DirectX 9)
- `hlsl_sm4` - HLSL Shader Model 4.0 (DirectX 10)
- `hlsl_sm5` - HLSL Shader Model 5.0 (DirectX 11)
- `hlsl_sm6` - HLSL Shader Model 6.0 (DirectX 12)

##### GLSL Targets
- `glsl_140` - GLSL 1.40 (OpenGL 3.1)
- `glsl_330` - GLSL 3.30 (OpenGL 3.3)
- `glsl_450` - GLSL 4.50 (OpenGL 4.5)
- `glsl_es_100` - GLSL ES 1.00 (OpenGL ES 2.0)
- `glsl_es_300` - GLSL ES 3.00 (OpenGL ES 3.0)
- `glsl_es_320` - GLSL ES 3.20 (OpenGL ES 3.2)

### Usage Examples

#### Basic Conversion
```bash
# Convert HLSL to GLSL
hlsloptconv -f glsl_450 -s vertex -o output.glsl input.hlsl

# Convert GLSL to HLSL
hlsloptconv -f hlsl_sm6 -s pixel -o output.hlsl input.hlsl

# Auto-detect input format
hlsloptconv -f glsl_450 -s vertex shader.txt
```

#### Advanced Usage
```bash
# High optimization with statistics
hlsloptconv -f glsl_450 -s vertex -O 4 --stats -o optimized.glsl input.hlsl

# Mobile target with fast math
hlsloptconv -f glsl_es_300 -s pixel --target mobile --fast-math input.hlsl

# With preprocessor definitions
hlsloptconv -f glsl_450 -s vertex -DUSE_TEXTURES=1 -DMAX_LIGHTS=4 input.hlsl

# Debug mode with AST dump
hlsloptconv -f glsl_450 -s vertex -d --backup -o debug.glsl input.hlsl
```

#### Pretty-print to Console
```bash
# Display optimized shader to console
hlsloptconv -f glsl_450 -s vertex -P input.hlsl

# Show optimization statistics
hlsloptconv -f glsl_450 -s vertex --stats input.hlsl
```

## C/C++ API

### Include Headers

```cpp
#include "hlsloptconv.h"
```

### Basic Data Types

```cpp
// Output format enumeration
enum OutputShaderFormat
{
    OSF_HLSL_SM3,      // HLSL Shader Model 3.0
    OSF_HLSL_SM4,      // HLSL Shader Model 4.0
    OSF_HLSL_SM5,      // HLSL Shader Model 5.0
    OSF_HLSL_SM6,      // HLSL Shader Model 6.0
    OSF_GLSL_140,      // GLSL 1.40
    OSF_GLSL_330,      // GLSL 3.30
    OSF_GLSL_450,      // GLSL 4.50
    OSF_GLSL_ES_100,   // GLSL ES 1.00
    OSF_GLSL_ES_300,   // GLSL ES 3.00
    OSF_GLSL_ES_320,   // GLSL ES 3.20
};

// Shader stage enumeration
enum ShaderStage
{
    SS_VERTEX,         // Vertex shader
    SS_PIXEL,          // Pixel/Fragment shader
    SS_GEOMETRY,       // Geometry shader
    SS_HULL,           // Hull shader (tessellation)
    SS_DOMAIN,         // Domain shader (tessellation)
    SS_COMPUTE,        // Compute shader
};

// Compilation configuration
struct CompileConfig
{
    OutputShaderFormat outputFmt;     // Output format
    ShaderStage shaderStage;          // Shader stage
    const char* entryPoint;           // Entry point function name
    const char** includePaths;        // Include search paths
    int includePathCount;             // Number of include paths
    const char** defines;             // Preprocessor definitions
    int defineCount;                  // Number of definitions
    int optimizationLevel;            // Optimization level (0-4)
    bool enableStats;                 // Enable optimization statistics
    bool fastMath;                    // Enable fast math optimizations
};
```

### Core Functions

```cpp
// Initialize the compiler
bool InitializeCompiler();

// Compile shader from string
bool CompileShaderFromString(
    const char* shaderSource,
    const CompileConfig* config,
    char** outputShader,
    char** errorMessage
);

// Compile shader from file
bool CompileShaderFromFile(
    const char* inputFile,
    const char* outputFile,
    const CompileConfig* config,
    char** errorMessage
);

// Free allocated memory
void FreeShaderString(char* shaderString);

// Cleanup compiler resources
void CleanupCompiler();
```

### Usage Example

```cpp
#include "hlsloptconv.h"
#include <stdio.h>

int main()
{
    // Initialize compiler
    if (!InitializeCompiler()) {
        printf("Failed to initialize compiler\n");
        return 1;
    }
    
    // Configure compilation
    CompileConfig config = {};
    config.outputFmt = OSF_GLSL_450;
    config.shaderStage = SS_VERTEX;
    config.entryPoint = "main";
    config.optimizationLevel = 2;
    config.enableStats = true;
    
    // Compile shader
    char* outputShader = nullptr;
    char* errorMessage = nullptr;
    
    const char* hlslSource = R"(
        float4 main(float3 pos : POSITION) : SV_POSITION {
            return float4(pos, 1.0);
        }
    )";
    
    if (CompileShaderFromString(hlslSource, &config, &outputShader, &errorMessage)) {
        printf("Compiled shader:\n%s\n", outputShader);
        FreeShaderString(outputShader);
    } else {
        printf("Compilation failed: %s\n", errorMessage);
        FreeShaderString(errorMessage);
    }
    
    // Cleanup
    CleanupCompiler();
    return 0;
}
```

## Error Handling

### Return Values
- `true` - Operation successful
- `false` - Operation failed (check error message)

### Error Messages
Error messages are allocated by the library and must be freed using `FreeShaderString()`.

### Common Error Types
- **Syntax Errors** - Invalid shader syntax
- **Semantic Errors** - Type mismatches, undefined variables
- **Optimization Errors** - Issues during optimization passes
- **I/O Errors** - File read/write problems

## Optimization Levels

- **Level 0** - No optimization, syntax validation only
- **Level 1** - Basic optimizations (constant folding, dead code elimination)
- **Level 2** - Standard optimizations (default, recommended)
- **Level 3** - Aggressive optimizations (may increase compile time)
- **Level 4** - Maximum optimizations (experimental features)

## Platform-Specific Notes

### Windows
- Full DirectX support in four.exe demo
- All features available

### Linux/macOS
- Core functionality available
- OpenGL support in demo (if compiled)
- Some Windows-specific features unavailable
